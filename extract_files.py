import json

def extract_file_names(arg1, debug=False):
    """
    从JSON数据中提取type为file的name

    Args:
        arg1: JSON字符串或字典对象
        debug: 是否显示调试信息

    Returns:
        list: 包含所有文件名的列表
    """
    try:
        # 如果输入是字符串，先解析为字典
        if isinstance(arg1, str):
            data = json.loads(arg1)
        else:
            data = arg1

        if debug:
            print(f"数据类型: {type(data)}")
            print(f"数据内容: {data}")

        # 检查数据是否为字典
        if not isinstance(data, dict):
            if debug:
                print(f"错误: 期望字典类型，但得到 {type(data)}")
            return []

        file_names = []

        # 检查是否有 'data' 键
        if 'data' not in data:
            if debug:
                print("错误: JSON中没有找到 'data' 键")
                print(f"可用的键: {list(data.keys())}")
            return []

        # 检查 data 是否为列表
        if not isinstance(data['data'], list):
            if debug:
                print(f"错误: 'data' 应该是列表，但得到 {type(data['data'])}")
            return []

        # 遍历data数组中的每个对象
        for i, data_obj in enumerate(data['data']):
            if debug:
                print(f"处理第 {i} 个数据对象: {type(data_obj)}")

            # 检查是否为字典
            if not isinstance(data_obj, dict):
                if debug:
                    print(f"跳过非字典对象: {data_obj}")
                continue

            # 检查是否有 items 键
            if 'items' not in data_obj:
                if debug:
                    print(f"对象中没有 'items' 键: {list(data_obj.keys())}")
                continue

            # 遍历items数组
            for j, item in enumerate(data_obj['items']):
                if debug:
                    print(f"  处理第 {j} 个item: {item}")

                # 检查item是否为字典
                if not isinstance(item, dict):
                    if debug:
                        print(f"  跳过非字典item: {item}")
                    continue

                # 检查type是否为file
                if item.get('type') == 'file':
                    file_name = item.get('name')
                    if debug:
                        print(f"  找到文件: {file_name}")
                    file_names.append(file_name)

        return file_names

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()
        return []

# 示例用法
if __name__ == "__main__":
    # 您的JSON数据
    arg1 = {
        "data": [
            {
                "items": [
                    {
                        "name": "6.1.1a",
                        "path": "6.1.1a/",
                        "type": "directory"
                    },
                    {
                        "name": "0.docx",
                        "path": "0.docx",
                        "type": "file"
                    },
                    {
                        "name": "1.docx",
                        "path": "1.docx",
                        "type": "file"
                    },
                    {
                        "name": "2.docx",
                        "path": "2.docx",
                        "type": "file"
                    }
                ],
                "path": "/zhengju/6.1.1a",
                "total_count": 4
            }
        ]
    }

    # 提取文件名（不显示调试信息）
    file_names = extract_file_names(arg1)
    print("提取到的文件名:")
    for name in file_names:
        print(f"- {name}")

    # 也可以作为JSON字符串处理
    json_string = json.dumps(arg1, ensure_ascii=False)
    file_names_from_string = extract_file_names(json_string)
    print(f"\n从JSON字符串提取的结果: {file_names_from_string}")

    # 如果需要调试信息，可以这样调用：
    print("\n=== 调试模式 ===")
    file_names_debug = extract_file_names(arg1, debug=True)
